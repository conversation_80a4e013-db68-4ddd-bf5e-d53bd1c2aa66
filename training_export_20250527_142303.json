{"training_data": {"total_images": 2, "average_quality": 0.0, "average_accuracy": 0.0}, "field_statistics": {"date_of_birth": {"total": 2, "correct": 2, "accuracy": 1.0}, "gender": {"total": 2, "correct": 2, "accuracy": 1.0}, "id_number": {"total": 2, "correct": 2, "accuracy": 1.0}, "name_en": {"total": 2, "correct": 2, "accuracy": 1.0}, "name_kh": {"total": 2, "correct": 2, "accuracy": 1.0}, "nationality": {"total": 2, "correct": 2, "accuracy": 1.0}}, "recent_performance": [[1, "current_model", "1.0", 0.0, 1.0, 0.0, 0.0, "2025-05-27 07:03:40", "{\"total_images\": 1, \"successful_extractions\": 0, \"field_accuracy\": {\"name\": 1.0, \"id_number\": 0.0, \"date_of_birth\": 0.0, \"gender\": 0.0}, \"average_confidence\": 0.0, \"extraction_rate\": 0.0}"]], "recommendations": ["Collect more training data (target: 500+ images)", "Focus on improving image quality in training data", "Add more diverse examples of poor quality images", "Implement active learning to identify difficult cases", "Create specialized models for different image quality levels", "Add more Khmer script specific training data", "Implement transfer learning from pre-trained OCR models"]}